import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DevelopersService } from '../../services/developers.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-developer-details',
  templateUrl: './developer-details.component.html',
  styleUrls: ['./developer-details.component.scss']
})
export class DeveloperDetailsComponent implements OnInit {
  developer: any = null;
  loading = true;
  developerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private developersService: DevelopersService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.developerId = params['developerId'];
      if (this.developerId) {
        this.loadDeveloperDetails();
      } else {
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  loadDeveloperDetails(): void {
    this.loading = true;
    
    this.developersService.getDeveloperById(this.developerId).subscribe({
      next: (response) => {
        console.log('Developer details:', response);
        this.developer = response.data || response;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading developer details:', error);
        this.loading = false;
        Swal.fire('Error', 'Failed to load developer details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-developers']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getStatusText(status: boolean): string {
    return status ? 'Active' : 'Inactive';
  }
}
